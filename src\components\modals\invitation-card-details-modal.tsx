import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CloseCircle, Calendar, Clock, Location } from 'iconsax-react';

interface TemplateItem {
  id?: string;
  name: string;
  preview_url?: string;
  image?: string;
}

interface InvitationFormData {
  title: string;
  subtitle: string;
  introduction: string;
  eventDate: string;
  eventTime: string;
  eventVenue: string;
  footer: string;
}

interface InvitationCardDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedTemplate: TemplateItem | null;
  onContinue: (formData: InvitationFormData) => void;
}

export const InvitationCardDetailsModal: React.FC<
  InvitationCardDetailsModalProps
> = ({ isOpen, onClose, selectedTemplate, onContinue }) => {
  const [formData, setFormData] = useState<InvitationFormData>({
    title: '',
    subtitle: '',
    introduction: '',
    eventDate: '',
    eventTime: '',
    eventVenue: '',
    footer: '',
  });

  const [showTimePicker, setShowTimePicker] = useState(false);

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        title: '',
        subtitle: '',
        introduction: '',
        eventDate: '',
        eventTime: '',
        eventVenue: '',
        footer: '',
      });
    }
  }, [isOpen]);

  // Handle escape key and body scroll
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const handleInputChange = (
    field: keyof InvitationFormData,
    value: string
  ) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleContinue = () => {
    onContinue(formData);
  };

  const timeOptions = [
    '12:00 AM',
    '12:30 AM',
    '1:00 AM',
    '1:30 AM',
    '2:00 AM',
    '2:30 AM',
    '3:00 AM',
    '3:30 AM',
    '4:00 AM',
    '4:30 AM',
    '5:00 AM',
    '5:30 AM',
    '6:00 AM',
    '6:30 AM',
    '7:00 AM',
    '7:30 AM',
    '8:00 AM',
    '8:30 AM',
    '9:00 AM',
    '9:30 AM',
    '10:00 AM',
    '10:30 AM',
    '11:00 AM',
    '11:30 AM',
    '12:00 PM',
    '12:30 PM',
    '1:00 PM',
    '1:30 PM',
    '2:00 PM',
    '2:30 PM',
    '3:00 PM',
    '3:30 PM',
    '4:00 PM',
    '4:30 PM',
    '5:00 PM',
    '5:30 PM',
    '6:00 PM',
    '6:30 PM',
    '7:00 PM',
    '7:30 PM',
    '8:00 PM',
    '8:30 PM',
    '9:00 PM',
    '9:30 PM',
    '10:00 PM',
    '10:30 PM',
    '11:00 PM',
    '11:30 PM',
  ];

  const isFormValid =
    formData.title.trim() &&
    formData.eventDate &&
    formData.eventTime &&
    formData.eventVenue.trim();

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.2 }}
        className="fixed inset-0 z-50 flex items-center justify-center bg-black/40 backdrop-blur-sm"
        onClick={onClose}>
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2 }}
          className="mx-4 w-full max-w-[1056px] max-h-[90vh] overflow-y-auto relative rounded-2xl bg-white shadow-xl sm:mx-0"
          onClick={(e) => e.stopPropagation()}>
          <button
            onClick={onClose}
            className="absolute right-4 top-4 z-10 rounded-full p-1 hover:bg-gray-100 transition-colors">
            <CloseCircle size={32} variant="Bulk" color="#4D55F2" />
          </button>
          <div className="flex  overflow-hidden rounded-2xl">
            {/* Left Side - Template Preview */}
            <div className="w-1/2  p-8 flex items-center justify-center">
              {selectedTemplate?.preview_url ? (
                <div className="w-full max-w-[446px]  bg-gradient-to-br from-purple-50 to-blue-50 p-6 aspect-[3/4] overflow-hidden rounded-[16px]">
                  <img
                    src={selectedTemplate.preview_url}
                    alt={selectedTemplate.name}
                    className="w-full h-full object-cover rounded-xl border-4 border-white -rotate-2"
                  />
                </div>
              ) : (
                <div className="w-full max-w-[280px] aspect-[3/4] rounded-xl bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-500">Template Preview</span>
                </div>
              )}
            </div>

            <div className="w-1/2 py-8 pr-8 overflow-y-auto">
              <div className="mb-6">
                <h2 className="text-2xl md:text-[32px] font-semibold text-gray-900 mb-2">
                  Invitation Card Details
                </h2>
                <p className="text-gray-600">
                  Please enter the details you want displayed on your invite{' '}
                </p>
              </div>

              <div className="space-y-5">
                {/* Title */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base focus:border-primary-650 focus:ring-1 focus:ring-primary-650"
                  />
                </div>

                {/* Subtitle */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subtitle
                  </label>
                  <textarea
                    value={formData.subtitle}
                    onChange={(e) =>
                      handleInputChange('subtitle', e.target.value)
                    }
                    placeholder="Write a brief description here"
                    rows={3}
                    className="w-full py-2.5 px-3.5 rounded-xl border border-grey-200 placeholder:text-grey-300 outline-none text-base focus:border-primary-650 focus:ring-1 focus:ring-primary-650 resize-none"
                  />
                </div>

                {/* Introduction */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Introduction
                  </label>
                  <input
                    type="text"
                    value={formData.introduction}
                    onChange={(e) =>
                      handleInputChange('introduction', e.target.value)
                    }
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base focus:border-primary-650 focus:ring-1 focus:ring-primary-650"
                  />
                </div>

               
  <div className="flex  gap-4">
            <div className="flex-1">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Event Date
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Select Date Range"
                  value={dateRangeText}
                  readOnly
                  onClick={() => setShowDatePicker(true)}
                  className="w-full py-2.5 px-3.5 pl-10 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowDatePicker(true)}>
                  <Calendar variant="Bulk" size="20" color="#292D32" />
                </div>
              </div>
            </div>

            <div className="w-[118px]">
              <label className="block text-grey-500 text-sm font-medium mb-2">
                Time of Event
              </label>
              <div className="relative">
                <input
                  type="text"
                  placeholder="00:00"
                  value={eventTime}
                  readOnly
                  onClick={() => setShowTimePicker(true)}
                  className="w-full py-2.5 pl-10  rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base cursor-pointer"
                />
                <div
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                  onClick={() => setShowTimePicker(true)}>
                  <Clock variant="Bulk" size="20" color="#292D32" />
                </div>

                {/* Time Picker Dropdown */}
                {showTimePicker && (
                  <div
                    ref={timePickerRef}
                    className="absolute z-10 mt-2 bg-white rounded-lg shadow-lg p-2 left-0 w-[120px] max-h-[200px] overflow-y-auto"
                    style={{ scrollbarWidth: 'thin' }}>
                    <div className="flex flex-col">
                      {timeOptions.map((time) => (
                        <button
                          key={time}
                          onClick={() => handleTimeSelect(time)}
                          className={`text-left px-3 py-2 hover:bg-primary-250 rounded-md text-sm ${
                            eventTime === time
                              ? 'bg-primary-250 text-primary-650 font-medium'
                              : 'text-grey-500'
                          }`}>
                          {time}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
                {/* Event Venue */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Venue
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={formData.eventVenue}
                      onChange={(e) =>
                        handleInputChange('eventVenue', e.target.value)
                      }
                      placeholder="Type in event venue"
                      className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base focus:border-primary-650 focus:ring-1 focus:ring-primary-650"
                    />
                    <Location
                      size={16}
                      variant="Bulk"
                      color="#4D55F2"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    />
                  </div>
                </div>

                {/* Footer */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Footer
                  </label>
                  <input
                    type="text"
                    value={formData.footer}
                    onChange={(e) =>
                      handleInputChange('footer', e.target.value)
                    }
                    placeholder="Enter Event Title"
                    className="w-full py-2.5 px-3.5 rounded-full border border-grey-200 placeholder:text-grey-300 outline-none text-base focus:border-primary-650 focus:ring-1 focus:ring-primary-650"
                  />
                </div>
              </div>

              {/* Continue Button */}
              <div className="mt-8 flex justify-end">
                <button
                  onClick={handleContinue}
                  disabled={!isFormValid}
                  className={`px-8 py-3 rounded-full font-semibold text-white transition-all duration-200 ${
                    isFormValid
                      ? 'bg-primary-650 hover:bg-primary-700 cursor-pointer'
                      : 'bg-gray-300 cursor-not-allowed'
                  }`}>
                  Continue →
                </button>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};
